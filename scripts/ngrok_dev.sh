#!/bin/bash

# Скрипт для разработки с ngrok
# Используется только для локального тестирования

echo "🚀 Запуск бота в режиме разработки с ngrok..."

# Проверить, установлен ли ngrok
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok не установлен. Установите его сначала."
    exit 1
fi

# Проверить наличие BOT_TOKEN
if [ -z "$BOT_TOKEN" ]; then
    echo "❌ BOT_TOKEN не установлен. Экспортируйте переменную или создайте .env файл"
    exit 1
fi

# Запустить Redis если не запущен
echo "🔴 Проверка Redis..."
if ! docker ps | grep -q redis; then
    echo "🐳 Запуск Redis контейнера..."
    docker run -d --name redis-local -p 6379:6379 redis:alpine redis-server --requirepass q3terhWD23WWDwdfsdt
    sleep 3
fi

# Запустить ngrok в фоне для порта 8000
echo "🌐 Запуск ngrok туннеля на порт 8000..."
ngrok http 8000 > /dev/null 2>&1 &
NGROK_PID=$!

# Подождать запуска ngrok
sleep 5

# Получить ngrok URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | jq -r '.tunnels[0].public_url' 2>/dev/null)

# Если jq не установлен, используем grep
if [ -z "$NGROK_URL" ] || [ "$NGROK_URL" = "null" ]; then
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o '"public_url":"https://[^"]*' | cut -d'"' -f4 | head -1)
fi

if [ -z "$NGROK_URL" ]; then
    echo "❌ Не удалось получить ngrok URL"
    kill $NGROK_PID 2>/dev/null
    exit 1
fi

echo "✅ ngrok URL: $NGROK_URL"

# Создать временный .env для webhook режима
echo "📝 Создание временной конфигурации..."
cat > .env.ngrok << EOF
BOT_TOKEN=$BOT_TOKEN
WEBHOOK_MODE=true
WEBHOOK_HOST=$NGROK_URL
WEBHOOK_PATH=/webhook
WEB_SERVER_HOST=0.0.0.0
WEB_SERVER_PORT=8000
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=q3terhWD23WWDwdfsdt
EOF

# Установить webhook
echo "🔗 Установка webhook..."
WEBHOOK_RESPONSE=$(curl -s -F "url=$NGROK_URL/webhook" "https://api.telegram.org/bot$BOT_TOKEN/setWebhook")

if echo "$WEBHOOK_RESPONSE" | grep -q '"ok":true'; then
    echo "✅ Webhook установлен успешно!"
    echo "🎉 Запуск бота в webhook режиме..."
    echo ""
    echo "📱 Напишите боту /start в Telegram"
    echo "🌐 ngrok интерфейс: http://localhost:4040"
    echo "📊 Статистика: $NGROK_URL/stats"
    echo "💚 Здоровье: $NGROK_URL/health"
    echo ""
    echo "⚠️  Для остановки нажмите Ctrl+C"

    # Функция очистки при завершении
    cleanup() {
        echo ""
        echo "🛑 Остановка..."
        echo "🔗 Удаление webhook..."
        curl -s "https://api.telegram.org/bot$BOT_TOKEN/deleteWebhook" > /dev/null
        echo "🌐 Остановка ngrok..."
        kill $NGROK_PID 2>/dev/null
        echo "📝 Удаление временной конфигурации..."
        rm -f .env.ngrok
        echo "✅ Очистка завершена"
        exit 0
    }

    trap cleanup INT

    # Запустить бота с временной конфигурацией
    export $(cat .env.ngrok | xargs) && python main.py
else
    echo "❌ Ошибка установки webhook: $WEBHOOK_RESPONSE"
    kill $NGROK_PID 2>/dev/null
    rm -f .env.ngrok
    exit 1
fi
