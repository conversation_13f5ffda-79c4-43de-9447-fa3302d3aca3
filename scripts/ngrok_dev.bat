@echo off
chcp 65001 >nul
echo 🚀 Запуск бота в режиме разработки с ngrok...

REM Проверить наличие ngrok
where ngrok >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ ngrok не установлен. Установите его сначала.
    pause
    exit /b 1
)

REM Проверить наличие BOT_TOKEN
if "%BOT_TOKEN%"=="" (
    echo ❌ BOT_TOKEN не установлен. Создайте .env файл или экспортируйте переменную
    pause
    exit /b 1
)

REM Запустить Redis если не запущен
echo 🔴 Проверка Redis...
docker ps | findstr redis >nul
if %errorlevel% neq 0 (
    echo 🐳 Запуск Redis контейнера...
    docker run -d --name redis-local -p 6379:6379 redis:alpine redis-server --requirepass q3terhWD23WWDwdfsdt
    timeout /t 3 >nul
)

REM Запустить ngrok в фоне
echo 🌐 Запуск ngrok туннеля на порт 8000...
start /b ngrok http 8000

REM Подождать запуска ngrok
timeout /t 5 >nul

REM Получить ngrok URL
for /f "tokens=*" %%i in ('curl -s http://localhost:4040/api/tunnels ^| findstr "public_url.*https" ^| findstr -o "https://[^\"]*"') do set NGROK_URL=%%i

if "%NGROK_URL%"=="" (
    echo ❌ Не удалось получить ngrok URL
    taskkill /f /im ngrok.exe >nul 2>nul
    pause
    exit /b 1
)

echo ✅ ngrok URL: %NGROK_URL%

REM Создать временный .env для webhook режима
echo 📝 Создание временной конфигурации...
(
echo BOT_TOKEN=%BOT_TOKEN%
echo WEBHOOK_MODE=true
echo WEBHOOK_HOST=%NGROK_URL%
echo WEBHOOK_PATH=/webhook
echo WEB_SERVER_HOST=0.0.0.0
echo WEB_SERVER_PORT=8000
echo REDIS_ENABLED=true
echo REDIS_HOST=localhost
echo REDIS_PORT=6379
echo REDIS_PASSWORD=q3terhWD23WWDwdfsdt
) > .env.ngrok

REM Установить webhook
echo 🔗 Установка webhook...
for /f "tokens=*" %%i in ('curl -s -F "url=%NGROK_URL%/webhook" "https://api.telegram.org/bot%BOT_TOKEN%/setWebhook"') do set WEBHOOK_RESPONSE=%%i

echo %WEBHOOK_RESPONSE% | findstr "ok.*true" >nul
if %errorlevel% equ 0 (
    echo ✅ Webhook установлен успешно!
    echo 🎉 Запуск бота в webhook режиме...
    echo.
    echo 📱 Напишите боту /start в Telegram
    echo 🌐 ngrok интерфейс: http://localhost:4040
    echo 📊 Статистика: %NGROK_URL%/stats
    echo 💚 Здоровье: %NGROK_URL%/health
    echo.
    echo ⚠️  Для остановки нажмите Ctrl+C
    echo.
    
    REM Запустить бота с временной конфигурацией
    python main.py
    
    REM Очистка при завершении
    echo.
    echo 🛑 Остановка...
    echo 🔗 Удаление webhook...
    curl -s "https://api.telegram.org/bot%BOT_TOKEN%/deleteWebhook" >nul
    echo 🌐 Остановка ngrok...
    taskkill /f /im ngrok.exe >nul 2>nul
    echo 📝 Удаление временной конфигурации...
    del .env.ngrok >nul 2>nul
    echo ✅ Очистка завершена
) else (
    echo ❌ Ошибка установки webhook: %WEBHOOK_RESPONSE%
    taskkill /f /im ngrok.exe >nul 2>nul
    del .env.ngrok >nul 2>nul
    pause
    exit /b 1
)

pause
